#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

/**
 * Create and configure the MCP server
 */
function createServer(): McpServer {
  const server = new McpServer({
    name: "home-mcp",
    version: "1.0.0",
  });

  // Add a simple ping tool that responds with "pong"
  server.tool(
    "ping",
    {
      // No parameters needed for ping
    },
    async () => ({
      content: [
        {
          type: "text",
          text: "pong",
        },
      ],
    })
  );

  return server;
}

/**
 * Main function to start the MCP server
 */
async function main(): Promise<void> {
  const server = createServer();

  // Create stdio transport for communication
  const transport = new StdioServerTransport();

  // Connect the server to the transport
  await server.connect(transport);

  // Log that the server is running (to stderr so it doesn't interfere with stdio communication)
  console.error("Home MCP server running on stdio");
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.error("Received SIGINT, shutting down gracefully");
  process.exit(0);
});

// Start the server
main().catch((error) => {
  console.error("Failed to start server:", error);
  process.exit(1);
});
