# home-mcp

A Model Context Protocol (MCP) server implementation in TypeScript.

## Features

- **Ping Tool**: A simple tool that responds with "pong" when called
- **TypeScript**: Written in TypeScript with strict type checking
- **ESM**: Uses ES modules for modern JavaScript compatibility
- **Node.js 22+**: Requires Node.js version 22.14.0 or higher

## Installation

```bash
# Install dependencies
yarn install

# Build the project
yarn build
```

## Usage

### Running the Server

```bash
# Start the server
yarn start
```

The server runs on stdio and can be used with any MCP-compatible client.

### Testing with MCP Inspector

```bash
# Test the server with the MCP Inspector
yarn test:inspector
```

This will open the MCP Inspector in your browser, allowing you to interact with the server and test the ping tool.

### Available Tools

- **ping**: Returns "pong" when called. No parameters required.

## Development

```bash
# Watch mode for development
yarn dev

# Clean build artifacts
yarn clean

# Build the project
yarn build
```

## Requirements

- Node.js >= 22.14.0
- Yarn package manager

## License

MIT
